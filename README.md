# Location Tracker App

A Flutter application that automatically tracks and sends location data to a remote API every 30 seconds.

## Features

- **Automatic Location Tracking**: Captures GPS coordinates every 30 seconds
- **API Integration**: Sends location data to a configurable API endpoint via HTTP POST
- **Permission Management**: Handles location permissions properly with user-friendly feedback
- **Real-time UI**: Displays current location, transmission status, and timestamps
- **Error Handling**: Comprehensive error handling for network and location issues
- **Manual Controls**: Start/stop tracking functionality

## Technical Implementation

### Dependencies
- `geolocator`: For accessing device GPS coordinates
- `http`: For making API requests
- Built-in `Timer.periodic`: For 30-second interval tracking

### Key Components
- **Location Service**: Handles GPS coordinate capture with high accuracy
- **API Communication**: Sends JSON-formatted location data via HTTP POST
- **Permission Handling**: Manages location permission requests and status
- **Timer Management**: Automatic 30-second interval tracking
- **State Management**: Real-time UI updates for location and transmission status

### Data Format
The app sends location data in the following JSON format:
```json
{
  "latitude": 37.7749,
  "longitude": -122.4194,
  "accuracy": 5.0,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "altitude": 100.5,
  "speed": 0.0
}
```

## Setup and Configuration

### 1. API Endpoint Configuration
Edit the `apiEndpoint` constant in `lib/main.dart`:
```dart
static const String apiEndpoint = 'https://your-api-endpoint.com/location';
```

### 2. Platform Permissions
The app is pre-configured with the necessary permissions:

**Android** (`android/app/src/main/AndroidManifest.xml`):
- `ACCESS_FINE_LOCATION`
- `ACCESS_COARSE_LOCATION`
- `INTERNET`

**iOS** (`ios/Runner/Info.plist`):
- `NSLocationWhenInUseUsageDescription`
- `NSLocationAlwaysAndWhenInUseUsageDescription`

### 3. Running the App
```bash
flutter pub get
flutter run
```

## Usage

1. **Launch the App**: The app automatically requests location permissions on startup
2. **Grant Permissions**: Allow location access when prompted
3. **Automatic Tracking**: If permissions are granted, tracking starts automatically
4. **Monitor Status**: View real-time location data and transmission status in the UI
5. **Manual Control**: Use Start/Stop buttons to control tracking manually

## UI Components

- **Permission Status**: Shows current location permission state
- **Current Location**: Displays latest GPS coordinates, accuracy, altitude, and speed
- **Transmission Status**: Shows API call results and timestamps
- **Error Messages**: Displays any location or network errors
- **Control Buttons**: Start and stop tracking manually
- **API Configuration**: Shows current endpoint and interval settings

## Error Handling

The app handles various error scenarios:
- Location services disabled
- Permission denied/denied forever
- Network connectivity issues
- API response errors
- GPS signal unavailable

## Use Cases

This app is suitable for:
- Fleet management systems
- Asset tracking
- Employee location monitoring
- Delivery tracking
- Emergency response systems
- Research data collection

## Development Notes

- The app runs in the foreground to maintain the 30-second interval
- Location accuracy is set to high for precise coordinates
- Network errors are handled gracefully with retry capability
- The UI provides comprehensive feedback for all states and errors
- Timer is properly disposed of to prevent memory leaks

## Testing

Run the included tests:
```bash
flutter test
```

The test suite verifies the app's UI components and basic functionality.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
