// This is a basic Flutter widget test for the Location Tracker app.

import 'package:flutter_test/flutter_test.dart';

import 'package:location/main.dart';

void main() {
  testWidgets('Location Tracker app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const LocationTrackerApp());

    // Verify that the app title is displayed.
    expect(find.text('Location Tracker'), findsOneWidget);

    // Verify that permission status is shown.
    expect(find.text('Permission Status'), findsOneWidget);

    // Verify that location section is shown.
    expect(find.text('Current Location'), findsOneWidget);

    // Verify that transmission status is shown.
    expect(find.text('Transmission Status'), findsOneWidget);

    // Verify that control buttons are present.
    expect(find.text('Start Tracking'), findsOneWidget);
    expect(find.text('Stop Tracking'), findsOneWidget);
  });
}
